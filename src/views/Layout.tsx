import Sidebar from "@/components/Sidebar";
import FloatingButtons from "@/components/FloatingButtons";
import { Outlet } from "@tanstack/react-router";
import { useState } from "react";

export default function Layout() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const handleSidebarToggle = (isCollapsed: boolean) => {
    setIsSidebarCollapsed(isCollapsed);
  };

  const handleFloatingToggle = () => {
    setIsSidebarCollapsed(false);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleFloatingToggle();
    }
  };

  return (
    <div className="flex h-full w-full flex-col">
      <div className="flex h-full w-full flex-row relative">
        <Sidebar onToggle={handleSidebarToggle} />
        <div className="flex-1 flex justify-center">
          <div className="flex h-full w-full flex-row justify-center overflow-clip bg-gray-200/60 dark:bg-gray-900">
            <Outlet />
          </div>
        </div>

        {/* Floating buttons when sidebar is collapsed */}
        {isSidebarCollapsed && (
          <FloatingButtons
            onToggle={handleFloatingToggle}
            onKeyDown={handleKeyDown}
          />
        )}
      </div>
    </div>
  );
}
