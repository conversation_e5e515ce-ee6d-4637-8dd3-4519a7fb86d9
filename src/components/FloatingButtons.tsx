import { But<PERSON> } from "@/components/ui/button";
import { ChevronRight, Plus } from "lucide-react";
import { Link } from "@tanstack/react-router";

interface FloatingButtonsProps {
  onToggle: () => void;
  onKeyDown: (event: React.KeyboardEvent) => void;
}

export default function FloatingButtons({ onToggle, onKeyDown }: FloatingButtonsProps) {
  return (
    <div className="fixed top-4 left-4 z-50 flex flex-col gap-2">
      {/* Toggle Button */}
      <Button
        size="sm"
        variant="default"
        className="h-10 w-10 p-0 shadow-xl bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700 hover:shadow-2xl transition-all duration-200 backdrop-blur-sm"
        onClick={onToggle}
        onKeyDown={onKeyDown}
        aria-label="Expand sidebar"
        aria-expanded={false}
        tabIndex={0}
      >
        <ChevronRight className="h-4 w-4 text-gray-700 dark:text-gray-200" />
      </Button>
      
      {/* Add Page Button */}
      <Link to="/pages">
        <Button
          size="sm"
          variant="default"
          className="h-10 w-10 p-0 shadow-xl bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700 hover:shadow-2xl transition-all duration-200 backdrop-blur-sm"
          aria-label="Create new page"
        >
          <Plus className="h-4 w-4 text-gray-700 dark:text-gray-200" />
        </Button>
      </Link>
    </div>
  );
}
