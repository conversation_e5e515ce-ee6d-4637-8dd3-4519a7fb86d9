import { usePages, PageData } from "@/hooks/usePages";
import { Link, useLocation } from "@tanstack/react-router";
import { cn } from "@/lib/utils";
import dayjs from "dayjs";
import Loader from "./Loader";
import { FileText, Plus, Menu, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import ProfileDropdown from "./ProfileDropdown";
import React, { useState } from "react";

interface SidebarProps {
  className?: string;
  onToggle?: (isCollapsed: boolean) => void;
}

interface SidebarItemProps {
  page: PageData;
  isActive: boolean;
}

const routes = [
  {
    label: "Home",
    to: "/pages",
  },
  {
    label: "Mermaid",
    to: "/mermaid",
  },
];

function SidebarItem({ page, isActive }: SidebarItemProps) {
  return (
    <Link
      to="/page/$id"
      params={{ id: page.page_id }}
      className={cn(
        "group flex w-full flex-col gap-1 rounded-lg border p-3 text-left transition-all hover:bg-gray-100 dark:hover:bg-gray-800",
        isActive
          ? "border-blue-500 bg-blue-50 dark:bg-blue-950/20"
          : "border-gray-200 dark:border-gray-700"
      )}
    >
      <div className="flex items-center gap-2">
        <FileText className="h-4 w-4 text-gray-500" />
        <span
          className={cn(
            "truncate text-sm font-medium",
            isActive ? "text-blue-700 dark:text-blue-300" : "text-gray-900 dark:text-gray-100"
          )}
        >
          {page.name || "Untitled"}
        </span>
      </div>
      <span className="text-xs text-gray-500 dark:text-gray-400">
        {dayjs(page.updated_at).format("MMM DD, YYYY")}
      </span>
    </Link>
  );
}

function EmptyState() {
  return (
    <div className="flex flex-col items-center justify-center p-6 text-center">
      <FileText className="h-12 w-12 text-gray-400 mb-4" />
      <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
        No pages yet
      </h3>
      <p className="text-xs text-gray-500 dark:text-gray-400 mb-4">
        Create your first drawing to get started
      </p>
      <Link to="/pages">
        <Button size="sm" variant="outline" className="gap-2">
          <Plus className="h-4 w-4" />
          Create Page
        </Button>
      </Link>
    </div>
  );
}

export default function Sidebar({ className, onToggle }: SidebarProps) {
  const { pages, isLoading } = usePages();
  const location = useLocation();
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Extract page ID from current location pathname
  const currentPageId = location.pathname.startsWith('/page/')
    ? location.pathname.split('/page/')[1]
    : null;

  const toggleSidebar = () => {
    const newCollapsedState = !isCollapsed;
    setIsCollapsed(newCollapsedState);
    onToggle?.(newCollapsedState);
  };

  // Notify parent of initial state
  React.useEffect(() => {
    onToggle?.(isCollapsed);
  }, [isCollapsed, onToggle]);

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleSidebar();
    }
  };

  // When collapsed, don't render sidebar - floating buttons will be handled by Layout
  if (isCollapsed) {
    return null;
  }

  // Render expanded sidebar
  return (
    <div
      className={cn(
        "flex h-full w-64 flex-col bg-white dark:bg-gray-900",
        className
      )}
    >
      {/* Header with Draw Logo */}
      <div className="flex items-center justify-between border-b border-gray-200 p-4 dark:border-gray-700">
        <Link to="/pages" className="flex items-center">
          <h1 className="font-virgil text-2xl font-bold text-gray-900 dark:text-gray-100">Draw</h1>
        </Link>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="ghost"
            className="h-8 w-8 p-0"
            onClick={toggleSidebar}
            onKeyDown={handleKeyDown}
            aria-label="Collapse sidebar"
            aria-expanded={true}
            tabIndex={0}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Link to="/pages">
            <Button size="sm" variant="ghost" className="h-8 w-8 p-0" aria-label="Create new page">
              <Plus className="h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>

      {/* Pages Content */}
      <div className="flex-1 overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center p-6">
            <Loader />
          </div>
        ) : pages.length === 0 ? (
          <EmptyState />
        ) : (
          <div className="h-full overflow-y-auto">
            <div className="space-y-2 p-4">
              {pages.map((page) => (
                <SidebarItem
                  key={page.page_id}
                  page={page}
                  isActive={currentPageId === page.page_id}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="border-t border-gray-200 p-4 dark:border-gray-700">
        <div className="space-y-2 mb-4">
          {routes.map(({ label, to }) => (
            <Link to={to} key={to}>
              {({ isActive }) => (
                <Button
                  className={cn(
                    "flex h-10 w-full items-center justify-center gap-3 text-sm font-light hover:font-bold",
                    isActive ? "font-bold" : "font-medium",
                  )}
                  variant="outline"
                >
                  {label}
                </Button>
              )}
            </Link>
          ))}
        </div>
        <div className="flex justify-center">
          <ProfileDropdown />
        </div>
      </div>
    </div>
  );
}
