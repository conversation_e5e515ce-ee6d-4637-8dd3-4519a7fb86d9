import { useEffect, useState, useCallback } from "react";
import {
  <PERSON><PERSON>ip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Loader from "@/components/Loader";
import { useTheme } from "@/components/theme-provider";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { Excalidraw, WelcomeScreen, Sidebar, Footer } from "@excalidraw/excalidraw";
import { NonDeletedExcalidrawElement } from "@excalidraw/excalidraw/element/types";
import { ExcalidrawImperativeAPI } from "@excalidraw/excalidraw/types";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { RefreshCcw, FileText, Plus, Home, Palette } from "lucide-react";
import { getDrawData, setDrawData } from "@/db/draw";
import { drawDataStore } from "@/stores/drawDataStore";


import { usePages } from "@/hooks/usePages";
import { Link } from "@tanstack/react-router";
import ProfileDropdown from "@/components/ProfileDropdown";
import dayjs from "dayjs";

type PageProps = {
  id: string;
};

export default function Page({ id }: PageProps) {
  const [excalidrawAPI, setExcalidrawAPI] =
    useState<ExcalidrawImperativeAPI | null>(null);
  const [name, setName] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const { theme } = useTheme();
  const queryClient = useQueryClient();

  const { pages, isLoading: pagesLoading } = usePages();
  const [sidebarDocked, setSidebarDocked] = useState(false);

  const { data, isLoading } = useQuery({
    queryKey: ["page", id],
    queryFn: () => getDrawData(id),
  });

  const mutation = useMutation({
    mutationFn: (data: {
      elements: NonDeletedExcalidrawElement[];
      name: string;
    }) => setDrawData(id, data.elements, data.name),
    onSuccess: () => {
      setIsSaving(false);
      // Invalidate the pages cache to update the sidebar
      queryClient.invalidateQueries({ queryKey: ["pages"] });
      // Also invalidate the current page cache to ensure consistency
      queryClient.invalidateQueries({ queryKey: ["page", id] });
    },
    onError: (error: Error) => {
      setIsSaving(false);
      toast("An error occurred while saving to the server", {
        description: error.message,
      });
    },
  });

  const { mutate } = mutation;

  async function updateScene() {
    if (data?.data && excalidrawAPI) {
      const elements = data.data[0].page_elements.elements;
      excalidrawAPI.updateScene({
        elements: elements,
        appState: { theme: theme },
      });
      setName(data.data[0].name);
    }
    if (data?.error) {
      toast("An error occurred", { description: data.error.message });
    }
  }

  const setSceneData = useCallback(async () => {
    if (excalidrawAPI) {
      const scene = excalidrawAPI.getSceneElements();
      const updatedAt = new Date().toISOString();

      const existingData = drawDataStore.getState().getPageData(id);

      if (JSON.stringify(existingData?.elements) !== JSON.stringify(scene)) {
        setIsSaving(true);
        // Save locally first
        drawDataStore.getState().setPageData(id, scene, updatedAt, name);

        // Then push to API
        mutate(
          {
            elements: scene as NonDeletedExcalidrawElement[],
            name,
          },
          {
            onSettled() {
              setIsSaving(false);
            },
          },
        );
      }
    }
  }, [excalidrawAPI, id, name, mutate]);

  useEffect(() => {
    if (!isLoading && data?.data && excalidrawAPI) {
      setTimeout(updateScene, 1000);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading, data, excalidrawAPI]);

  useEffect(() => {
    const interval = setInterval(() => {
      setSceneData();
    }, 3000);

    return () => clearInterval(interval);
  }, [setSceneData]);

  useEffect(() => {
    // Load data from local storage if available
    const localData = drawDataStore.getState().getPageData(id);
    if (localData && excalidrawAPI) {
      excalidrawAPI.updateScene({
        elements: localData.elements,
        appState: { theme: theme },
      });
      setName(localData.name);
    }
  }, [id, excalidrawAPI, theme]);

  return (
    <div className="flex w-full flex-col">
      <div className="h-full w-full">
        {isLoading ? (
          <Loader />
        ) : (
          <Excalidraw
            excalidrawAPI={(api) => setExcalidrawAPI(api)}
            initialData={{ appState: { theme: theme } }}
            renderTopRightUI={() => (
              <div className="flex gap-2">
                <Input
                  onChange={(e) => setName(e.target.value)}
                  value={name}
                  className="h-9 w-40"
                  placeholder="Page Title"
                />
                <Button
                  variant="secondary"
                  onClick={setSceneData}
                  disabled={isSaving}
                  size="sm"
                >
                  {isSaving ? "Saving..." : "Save"}
                </Button>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={updateScene}
                      >
                        <RefreshCcw className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        Refreshes the page. This removes any unsaved changes.
                        Use with caution.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            )}
            theme={theme === "dark" ? "dark" : "light"}
            autoFocus
          >
            <WelcomeScreen />

            {/* Excalidraw Native Sidebar */}
            <Sidebar name="pages" docked={sidebarDocked} onDock={setSidebarDocked}>
              <Sidebar.Header>
                <h2 className="text-lg font-semibold">Draw</h2>
              </Sidebar.Header>

              <Sidebar.Tabs>
                <Sidebar.Tab tab="pages">
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-sm font-medium">Pages</h3>
                      <Link to="/pages">
                        <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                          <Plus className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>

                    {pagesLoading ? (
                      <div className="flex items-center justify-center p-6">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                      </div>
                    ) : pages.length === 0 ? (
                      <div className="text-center p-6">
                        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-sm text-gray-500">No pages yet</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {pages.map((page) => (
                          <Link
                            key={page.page_id}
                            to="/page/$id"
                            params={{ id: page.page_id }}
                            className="block p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800"
                          >
                            <div className="flex items-center gap-2">
                              <FileText className="h-4 w-4 text-gray-500" />
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium truncate">
                                  {page.name || "Untitled"}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {dayjs(page.updated_at).format("MMM DD, YYYY")}
                                </p>
                              </div>
                            </div>
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                </Sidebar.Tab>

                <Sidebar.Tab tab="navigation">
                  <div className="p-4 space-y-4">
                    <h3 className="text-sm font-medium mb-4">Navigation</h3>

                    <div className="space-y-2">
                      <Link to="/pages" className="block">
                        <Button variant="outline" className="w-full justify-start gap-2">
                          <Home className="h-4 w-4" />
                          Home
                        </Button>
                      </Link>

                      <Link to="/mermaid" className="block">
                        <Button variant="outline" className="w-full justify-start gap-2">
                          <Palette className="h-4 w-4" />
                          Mermaid
                        </Button>
                      </Link>
                    </div>

                    <div className="pt-4 border-t">
                      <ProfileDropdown />
                    </div>
                  </div>
                </Sidebar.Tab>

                <Sidebar.TabTriggers>
                  <Sidebar.TabTrigger tab="pages">
                    <FileText className="h-4 w-4" />
                  </Sidebar.TabTrigger>
                  <Sidebar.TabTrigger tab="navigation">
                    <Home className="h-4 w-4" />
                  </Sidebar.TabTrigger>
                </Sidebar.TabTriggers>
              </Sidebar.Tabs>
            </Sidebar>

            {/* Footer with Sidebar Trigger */}
            <Footer>
              <Sidebar.Trigger name="pages" tab="pages">
                <FileText className="h-4 w-4 mr-2" />
                Pages
              </Sidebar.Trigger>
            </Footer>
          </Excalidraw>
        )}
      </div>
    </div>
  );
}
